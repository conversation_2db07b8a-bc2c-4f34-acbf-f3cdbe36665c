package com.example.aimusicplayer.ui.discovery

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.aimusicplayer.R
import com.example.aimusicplayer.databinding.FragmentDiscoveryBinding
import com.example.aimusicplayer.ui.adapter.PlaylistAdapter
import com.example.aimusicplayer.viewmodel.DiscoveryViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 音乐探索视图，展示音乐发现、热门推荐、排行榜等内容
 * 使用MVVM架构和Hilt依赖注入
 */
@AndroidEntryPoint
class DiscoveryFragment : Fragment(), PlaylistAdapter.OnPlaylistClickListener {

    companion object {
        private const val TAG = "DiscoveryFragment"
    }

    private var _binding: FragmentDiscoveryBinding? = null
    private val binding get() = _binding!!

    private val viewModel: DiscoveryViewModel by viewModels()
    private lateinit var playlistAdapter: PlaylistAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDiscoveryBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupRecyclerView()
        setupObservers()
        loadData()
    }

    private fun setupRecyclerView() {
        playlistAdapter = PlaylistAdapter(emptyList(), this)
        binding.recyclerViewToplists.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = playlistAdapter
        }
    }

    private fun setupObservers() {
        // 观察排行榜数据
        viewModel.toplistList.observe(viewLifecycleOwner) { playlists ->
            playlists?.let {
                playlistAdapter.updatePlaylists(it)
            }
        }

        // 观察加载状态
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        // 观察错误信息
        viewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            if (!errorMessage.isNullOrEmpty()) {
                Toast.makeText(context, errorMessage, Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun loadData() {
        viewModel.loadToplists()
    }

    override fun onPlaylistClick(position: Int) {
        // TODO: 实现歌单点击功能，导航到歌单详情页面
        Toast.makeText(context, "点击了排行榜 #$position", Toast.LENGTH_SHORT).show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

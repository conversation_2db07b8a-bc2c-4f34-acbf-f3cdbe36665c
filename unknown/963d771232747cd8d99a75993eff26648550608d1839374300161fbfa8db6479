<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/bg_bottom_sheet">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="8dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="心动模式"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@android:color/white" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <ImageButton
            android:id="@+id/button_heart_mode_refresh"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_refresh"
            app:tint="@android:color/white"
            android:contentDescription="刷新" />

        <ImageButton
            android:id="@+id/button_heart_mode_close"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            app:tint="@android:color/white"
            android:contentDescription="关闭" />
    </LinearLayout>

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#33FFFFFF" />

    <!-- 说明文本 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:text="根据当前歌曲为您推荐相似的音乐，开启心动模式后将自动播放这些歌曲。"
        android:textColor="#CCFFFFFF"
        android:textSize="14sp" />

    <!-- 相似歌曲列表 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="200dp"
        android:maxHeight="400dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_heart_mode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="8dp"
            android:clipToPadding="false"
            android:overScrollMode="never"
            tools:listitem="@layout/item_playlist_song" />

        <FrameLayout
            android:id="@+id/loading_view_heart_mode"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">

            <ProgressBar
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="center" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="48dp"
                android:text="正在寻找相似歌曲..."
                android:textColor="@android:color/white"
                android:textSize="14sp" />
        </FrameLayout>
    </FrameLayout>

    <!-- 开始按钮 -->
    <Button
        android:id="@+id/btn_start_heart_mode"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/bg_button"
        android:text="开启心动模式"
        android:textColor="@android:color/white"
        android:textSize="16sp" />
</LinearLayout>

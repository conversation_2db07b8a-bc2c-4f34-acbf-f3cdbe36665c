<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="12dp"
    android:paddingStart="8dp"
    android:paddingEnd="0dp"
    android:background="?attr/selectableItemBackground">

    <!-- 用户头像 - 增大尺寸，更适合车载场景 -->
    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/image_avatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:contentDescription="@string/user_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:civ_border_width="1dp"
        app:civ_border_color="#20000000"
        tools:src="@drawable/default_avatar" />

    <!-- 用户名 - 增大字体，更适合车载场景 -->
    <TextView
        android:id="@+id/text_username"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="?textColorPrimary"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/text_reply_time"
        app:layout_constraintStart_toEndOf="@id/image_avatar"
        app:layout_constraintTop_toTopOf="@id/image_avatar"
        tools:text="用户名" />

    <!-- 回复时间 -->
    <TextView
        android:id="@+id/text_reply_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?textColorSecondary"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/text_username"
        tools:text="2小时前" />

    <!-- 回复内容 - 增大字体和行间距，更适合车载场景 -->
    <TextView
        android:id="@+id/text_reply_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="?textColorPrimary"
        android:textSize="16sp"
        android:lineSpacingExtra="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/text_username"
        app:layout_constraintTop_toBottomOf="@id/text_username"
        tools:text="这是一条回复内容，可能会很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长" />

    <!-- 互动区域 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="@id/text_reply_content"
        app:layout_constraintTop_toBottomOf="@id/text_reply_content">

        <!-- 点赞区域 -->
        <LinearLayout
            android:id="@+id/layout_reply_like"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="6dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:id="@+id/image_reply_like"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_like_outline" />

            <TextView
                android:id="@+id/text_reply_like_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:textColor="?textColorSecondary"
                android:textSize="14sp"
                tools:text="123" />
        </LinearLayout>

        <!-- 回复按钮 -->
        <TextView
            android:id="@+id/text_reply_to_reply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:padding="6dp"
            android:text="回复"
            android:textColor="?textColorSecondary"
            android:textSize="14sp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

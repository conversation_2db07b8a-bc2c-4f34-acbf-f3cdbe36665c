<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_blue_purple"
    tools:context=".ui.main.MainActivity">

    <!-- Navigation Host Fragment -->
    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/nav_host_fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:defaultNavHost="true"
        app:navGraph="@navigation/nav_graph" />

    <!-- 旧的Fragment容器，保留以兼容旧代码 -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <!-- 加载动画 -->
    <com.example.aimusicplayer.ui.widget.LottieLoadingView
        android:id="@+id/loading_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        android:elevation="10dp"
        app:lottieAnimationAsset="data_loading.json"
        app:loadingMessage="加载中..."
        app:autoPlay="true"
        app:loop="true" />

    <!-- 侧边导航栏 - 纯黑背景 -->
    <LinearLayout
        android:id="@+id/sidebar_nav"
        android:layout_width="100dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/sidebar_background"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:gravity="center_horizontal"
        android:elevation="6dp"
        android:weightSum="6"
        android:layout_marginRight="1dp"
        android:layout_marginTop="1dp"
        android:layout_marginBottom="1dp"
        android:layout_marginLeft="1dp"
        android:padding="1dp"
        android:clipToPadding="false">

        <!-- 播放器按钮 - 音乐图标 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_player"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:src="@drawable/icon_music"
                android:background="@drawable/nav_button_background"
                android:contentDescription="播放器" />

            <View
                android:layout_width="40dp"
                android:layout_height="4dp"
                android:background="@color/nav_indicator"
                android:layout_marginTop="4dp"
                android:visibility="visible"
                android:id="@+id/nav_player_indicator" />
        </LinearLayout>

        <!-- 我的音乐库按钮 - 爱心图标 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_library"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:src="@drawable/icon_heart"
                android:background="@drawable/nav_button_background"
                android:contentDescription="我的音乐库" />

            <View
                android:layout_width="40dp"
                android:layout_height="4dp"
                android:background="@color/nav_indicator"
                android:layout_marginTop="4dp"
                android:visibility="gone"
                android:id="@+id/nav_library_indicator" />
        </LinearLayout>

        <!-- 音乐探索按钮 - 地球图标 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_discovery"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:src="@drawable/icon_globe"
                android:background="@drawable/nav_button_background"
                android:contentDescription="音乐探索" />

            <View
                android:layout_width="40dp"
                android:layout_height="4dp"
                android:background="@color/nav_indicator"
                android:layout_marginTop="4dp"
                android:visibility="gone"
                android:id="@+id/nav_discovery_indicator" />
        </LinearLayout>

        <!-- 驾驶模式按钮 - 汽车图标 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_driving"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:src="@drawable/icon_car"
                android:background="@drawable/nav_button_background"
                android:contentDescription="驾驶模式" />

            <View
                android:layout_width="40dp"
                android:layout_height="4dp"
                android:background="@color/nav_indicator"
                android:layout_marginTop="4dp"
                android:visibility="gone"
                android:id="@+id/nav_driving_indicator" />
        </LinearLayout>

        <!-- 用户中心按钮 - 用户图标 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_profile"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:src="@drawable/icon_user"
                android:background="@drawable/nav_button_background"
                android:contentDescription="用户中心" />

            <View
                android:layout_width="40dp"
                android:layout_height="4dp"
                android:background="@color/nav_indicator"
                android:layout_marginTop="4dp"
                android:visibility="gone"
                android:id="@+id/nav_profile_indicator" />
        </LinearLayout>

        <!-- 设置按钮 - 齿轮图标 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/nav_settings"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:src="@drawable/icon_settings"
                android:background="@drawable/nav_button_background"
                android:contentDescription="设置" />

            <View
                android:layout_width="40dp"
                android:layout_height="4dp"
                android:background="@color/nav_indicator"
                android:layout_marginTop="4dp"
                android:visibility="gone"
                android:id="@+id/nav_settings_indicator" />
        </LinearLayout>
    </LinearLayout>

    <!-- 侧边栏右侧的菜单按钮 - 初始状态为左上角 -->
    <ImageView
        android:id="@+id/btn_menu_right"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_alignParentLeft="true"
        android:layout_marginTop="16dp"
        android:layout_marginLeft="16dp"
        android:padding="12dp"
        android:src="@drawable/ic_back_improved"
        android:background="@drawable/back_button_background"
        android:contentDescription="菜单"
        android:elevation="8dp" />
</RelativeLayout>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/sakura_dialog_background">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="手机号登录"
        android:textColor="@color/sakura_text_primary"
        android:textSize="20sp"
        android:textStyle="bold"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_subtitle"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="请输入手机号和密码登录"
            android:textColor="@color/sakura_text_secondary"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/tv_switch_login_method"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="验证码登录"
            android:textColor="@color/sakura_accent"
            android:textSize="14sp"
            android:padding="4dp"/>
    </LinearLayout>

    <TextView
        android:id="@+id/tv_phone_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="手机号码"
        android:textColor="@color/sakura_text_primary"
        android:textSize="14sp"/>

    <EditText
        android:id="@+id/et_phone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@drawable/sakura_edit_background"
        android:hint="请输入手机号码"
        android:textColor="@color/text_primary"
        android:textColorHint="@color/text_secondary"
        android:inputType="phone"
        android:padding="12dp"
        android:maxLines="1"/>

    <!-- 密码登录视图 -->
    <LinearLayout
        android:id="@+id/password_login_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_password_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="密码"
            android:textColor="@color/sakura_text_primary"
            android:textSize="14sp"/>

        <EditText
            android:id="@+id/et_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/sakura_edit_background"
            android:hint="请输入密码"
            android:textColor="@color/text_primary"
            android:textColorHint="@color/text_secondary"
            android:inputType="textPassword"
            android:padding="12dp"
            android:maxLines="1"/>
    </LinearLayout>

    <!-- 验证码登录视图 -->
    <LinearLayout
        android:id="@+id/captcha_login_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/tv_captcha_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="验证码"
            android:textColor="@color/sakura_text_primary"
            android:textSize="14sp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/et_captcha"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:background="@drawable/sakura_edit_background"
                android:hint="请输入验证码"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_secondary"
                android:inputType="number"
                android:padding="12dp"
                android:maxLines="1"/>

            <Button
                android:id="@+id/btn_get_captcha"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="8dp"
                android:background="@drawable/sakura_button_secondary"
                android:text="获取验证码"
                android:textColor="@color/sakura_text_primary"
                android:textSize="12sp"/>
        </LinearLayout>
    </LinearLayout>

    <!-- 加载状态视图 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp">

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_gravity="center"
            android:indeterminateTint="@color/sakura_accent"
            android:visibility="gone" />

        <!-- 移除文字提示，仅使用动画 -->
        <TextView
            android:id="@+id/tv_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="40dp"
            android:textColor="@color/sakura_accent"
            android:textSize="14sp"
            android:visibility="gone" />
    </FrameLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:background="@drawable/sakura_button_secondary"
            android:text="取消"
            android:textColor="@color/sakura_text_primary" />

        <Button
            android:id="@+id/btn_login"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:background="@drawable/sakura_button"
            android:text="登录"
            android:textColor="@color/text_light" />
    </LinearLayout>

</LinearLayout>
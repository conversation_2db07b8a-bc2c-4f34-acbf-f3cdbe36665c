<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:contentScrim="@color/black"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_collapseMode="parallax">

                <!-- 用户封面背景 -->
                <ImageView
                    android:id="@+id/iv_user_cover"
                    android:layout_width="match_parent"
                    android:layout_height="140dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/default_background"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- 渐变遮罩 -->
                <View
                    android:id="@+id/view_gradient_overlay"
                    android:layout_width="match_parent"
                    android:layout_height="140dp"
                    android:background="@drawable/gradient_overlay"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- 用户头像 -->
                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/iv_user_avatar"
                    android:layout_width="100dp"
                    android:layout_height="100dp"
                    android:layout_marginStart="40dp"
                    android:layout_marginTop="90dp"
                    android:src="@drawable/default_avatar"
                    app:civ_border_color="@color/colorPrimary"
                    app:civ_border_width="3dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- 用户信息 -->
                <LinearLayout
                    android:id="@+id/layout_user_info"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="20dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="@id/iv_user_avatar"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/iv_user_avatar">

                    <!-- 用户名 -->
                    <TextView
                        android:id="@+id/tv_username"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="用户名"
                        android:textColor="@android:color/white"
                        android:textSize="24sp"
                        android:textStyle="bold" />

                    <!-- 用户标签 -->
                    <LinearLayout
                        android:id="@+id/layout_user_tags"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <!-- VIP标签 -->
                        <TextView
                            android:id="@+id/tv_vip_tag"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/rounded_status_background"
                            android:backgroundTint="#33F1C40F"
                            android:drawableStart="@drawable/ic_favorite"
                            android:drawablePadding="4dp"
                            android:drawableTint="#F1C40F"
                            android:padding="4dp"
                            android:text="VIP会员"
                            android:textColor="#F1C40F"
                            android:textSize="12sp"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <!-- 等级标签 -->
                        <TextView
                            android:id="@+id/tv_level_tag"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:background="@drawable/rounded_status_background"
                            android:backgroundTint="#333498DB"
                            android:padding="4dp"
                            android:text="Lv.6"
                            android:textColor="#3498DB"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <!-- 用户签名 -->
                    <TextView
                        android:id="@+id/tv_signature"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:ellipsize="end"
                        android:maxLines="2"
                        android:text="音乐是生活的调味剂，让心灵得到治愈"
                        android:textColor="#BBBBBB"
                        android:textSize="14sp" />
                </LinearLayout>

                <!-- 用户统计信息 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/card_user_stats"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="20dp"
                    app:cardBackgroundColor="#1AFFFFFF"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="0dp"
                    app:layout_constraintTop_toBottomOf="@id/iv_user_avatar">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="15dp">

                        <!-- 收藏歌曲 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_liked_songs_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="268"
                                android:textColor="@android:color/white"
                                android:textSize="22sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:text="收藏歌曲"
                                android:textColor="#999999"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <!-- 分隔线 -->
                        <View
                            android:layout_width="1dp"
                            android:layout_height="match_parent"
                            android:background="#1AFFFFFF" />

                        <!-- 创建歌单 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_playlists_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="32"
                                android:textColor="@android:color/white"
                                android:textSize="22sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:text="创建歌单"
                                android:textColor="#999999"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <!-- 分隔线 -->
                        <View
                            android:layout_width="1dp"
                            android:layout_height="match_parent"
                            android:background="#1AFFFFFF" />

                        <!-- 关注歌手 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_followed_artists_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="86"
                                android:textColor="@android:color/white"
                                android:textSize="22sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:text="关注歌手"
                                android:textColor="#999999"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <!-- 分隔线 -->
                        <View
                            android:layout_width="1dp"
                            android:layout_height="match_parent"
                            android:background="#1AFFFFFF" />

                        <!-- 收听时长 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tv_listening_hours"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="124"
                                android:textColor="@android:color/white"
                                android:textSize="22sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:text="收听时长(小时)"
                                android:textColor="#999999"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- 底部间距 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="20dp"
                    app:layout_constraintTop_toBottomOf="@id/card_user_stats" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <!-- 内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 账户信息标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="账户信息"
                    android:textColor="@android:color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_more"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="更多 >"
                    android:textColor="#999999"
                    android:textSize="14sp" />
            </LinearLayout>

            <!-- 账户信息列表 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="#1AFFFFFF"
                app:cardCornerRadius="10dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- 手机号 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="15dp">

                        <TextView
                            android:layout_width="80dp"
                            android:layout_height="wrap_content"
                            android:text="手机号"
                            android:textColor="#999999"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_phone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="138****6789"
                            android:textColor="@android:color/white"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#1AFFFFFF" />

                    <!-- 邮箱 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="15dp">

                        <TextView
                            android:layout_width="80dp"
                            android:layout_height="wrap_content"
                            android:text="邮箱"
                            android:textColor="#999999"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="<EMAIL>"
                            android:textColor="@android:color/white"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#1AFFFFFF" />

                    <!-- 会员状态 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="15dp">

                        <TextView
                            android:layout_width="80dp"
                            android:layout_height="wrap_content"
                            android:text="会员状态"
                            android:textColor="#999999"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_vip_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="年费会员 (有效期至2024年12月)"
                            android:textColor="#F1C40F"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#1AFFFFFF" />

                    <!-- 注册时间 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="15dp">

                        <TextView
                            android:layout_width="80dp"
                            android:layout_height="wrap_content"
                            android:text="注册时间"
                            android:textColor="#999999"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tv_register_time"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="2021年03月15日"
                            android:textColor="@android:color/white"
                            android:textSize="14sp" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- 操作按钮 -->
            <Button
                android:id="@+id/btn_logout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:background="@drawable/button_background"
                android:text="退出登录"
                android:textColor="@android:color/white" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- 加载进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

    <!-- 错误提示 -->
    <TextView
        android:id="@+id/tv_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="加载失败，点击重试"
        android:textColor="@android:color/white"
        android:visibility="gone" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>

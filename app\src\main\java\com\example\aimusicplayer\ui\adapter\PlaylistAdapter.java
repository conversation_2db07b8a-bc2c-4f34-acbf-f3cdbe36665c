package com.example.aimusicplayer.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.example.aimusicplayer.R;
import com.example.aimusicplayer.model.TopListResponse;
import com.example.aimusicplayer.utils.DiffCallbacks;
import com.example.aimusicplayer.utils.ImageUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 播放列表适配器
 * 使用DiffUtil优化更新
 */
public class PlaylistAdapter extends RecyclerView.Adapter<PlaylistAdapter.PlaylistViewHolder> {

    private List<TopListResponse.PlayList> playlists;
    private final OnPlaylistClickListener listener;
    private int lastAnimatedPosition = -1;
    private boolean animationsEnabled = true;

    public interface OnPlaylistClickListener {
        void onPlaylistClick(int position);
    }

    public PlaylistAdapter(List<TopListResponse.PlayList> playlists, OnPlaylistClickListener listener) {
        this.playlists = playlists != null ? new ArrayList<>(playlists) : new ArrayList<>();
        this.listener = listener;
    }

    @NonNull
    @Override
    public PlaylistViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_playlist, parent, false);
        return new PlaylistViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PlaylistViewHolder holder, int position) {
        TopListResponse.PlayList playlist = playlists.get(position);

        holder.tvPlaylistName.setText(playlist.getName());
        holder.tvPlaylistDescription.setText(playlist.getDescription());
        holder.tvUpdateFrequency.setText("每日更新");

        // 使用ImageLoader加载封面图片
        if (playlist.getCoverImgUrl() != null && !playlist.getCoverImgUrl().isEmpty()) {
            ImageUtils.loadImage(
                holder.ivPlaylistCover.getContext(),
                playlist.getCoverImgUrl(),
                holder.ivPlaylistCover,
                R.drawable.default_cover,
                R.drawable.default_cover
            );
        } else {
            holder.ivPlaylistCover.setImageResource(R.drawable.default_cover);
        }

        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onPlaylistClick(position);
            }
        });

        // 应用动画效果
        if (animationsEnabled && position > lastAnimatedPosition) {
            Animation animation = AnimationUtils.loadAnimation(
                    holder.itemView.getContext(), R.anim.item_animation_from_right);
            holder.itemView.startAnimation(animation);
            lastAnimatedPosition = position;
        }
    }

    @Override
    public int getItemCount() {
        return playlists != null ? playlists.size() : 0;
    }

    /**
     * 更新播放列表数据
     * 使用DiffUtil计算差异，高效更新UI
     * @param newPlaylists 新的播放列表数据
     */
    public void updatePlaylists(List<TopListResponse.PlayList> newPlaylists) {
        if (newPlaylists == null) {
            newPlaylists = new ArrayList<>();
        }

        // 使用DiffUtil计算差异
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(
                new DiffCallbacks.PlaylistDiffCallback(this.playlists, newPlaylists));

        // 更新数据
        this.playlists = new ArrayList<>(newPlaylists);

        // 应用差异
        diffResult.dispatchUpdatesTo(this);

        // 重置动画位置
        lastAnimatedPosition = -1;
    }

    /**
     * 启用/禁用动画
     * @param enabled 是否启用动画
     */
    public void setAnimationsEnabled(boolean enabled) {
        this.animationsEnabled = enabled;
    }

    static class PlaylistViewHolder extends RecyclerView.ViewHolder {
        ImageView ivPlaylistCover;
        TextView tvPlaylistName;
        TextView tvPlaylistDescription;
        TextView tvUpdateFrequency;

        public PlaylistViewHolder(@NonNull View itemView) {
            super(itemView);
            ivPlaylistCover = itemView.findViewById(R.id.ivPlaylistCover);
            tvPlaylistName = itemView.findViewById(R.id.tvPlaylistName);
            tvPlaylistDescription = itemView.findViewById(R.id.tvPlaylistDescription);
            tvUpdateFrequency = itemView.findViewById(R.id.tvUpdateFrequency);
        }
    }
}
